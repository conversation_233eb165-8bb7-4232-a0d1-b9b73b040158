package com.siemens.spine.db.entity.elcon;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "CONVEYOR")
public class Conveyor {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)

  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Size(max = 255)
  @Column(name = "SYS_TYPE")
  private String sysType;

  @Size(max = 255)
  @Column(name = "CONV_TYPE")
  private String convType;

  @Size(max = 255)
  @Column(name = "MECHANICAL_NAME")
  private String mechanicalName;

  @Column(name = "LENGTH")
  private Long length;

  @Column(name = "SPEED", precision = 10, scale = 4)
  private BigDecimal speed;

  @Column(name = "THROUGHPUT")
  private Long throughput;

  @Column(name = "JAM_AREA_FLAG")
  private Boolean jamAreaFlag;

  @Size(max = 5)
  @Column(name = "OVERDRIVE", length = 5)
  private String overdrive;

  @Column(name = "SW_MASTER_UPSTREAM")
  private Long swMasterUpstream;

  @Column(name = "SW_MASTER_DOWNSTREAM")
  private Long swMasterDownstream;

  @Column(name = "TRACKING")
  private Long tracking;

  @Column(name = "SEND_UNIT_FILLING")
  private Boolean sendUnitFilling;

}