package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "AREA")
public class EArea {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Size(max = 255)
  @Column(name = "AREA_KIND")
  private String areaKind;

  @Size(max = 255)
  @Column(name = "AREA_NAME")
  private String areaName;

  @Size(max = 255)
  @Column(name = "FUNCTION_TYPE")
  private String functionType;

  @Size(max = 255)
  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "PLC_SW_ID")
  private Long plcSwId;

  @Column(name = "GLOBAL_AREA_ID")
  private Long globalAreaId;

}
