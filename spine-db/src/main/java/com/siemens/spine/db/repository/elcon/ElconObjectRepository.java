package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.EObjectEntity;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elconObjects", collectionResourceRel = "elconObjects")
@ApplicationScoped
public class ElconObjectRepository extends AbstractRestCRUDRepository<EObjectEntity, Long> {

    @Override
    protected boolean isNew(EObjectEntity entity) {
        return entity.getObjectId() == null;
    }

}
