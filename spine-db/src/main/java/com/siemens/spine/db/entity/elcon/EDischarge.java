package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "DISCHARGE")
public class EDischarge {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)

  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Column(name = "DIS_ORDER_INDEX")
  private Integer disOrderIndex;

  @Size(max = 127)
  @Column(name = "TRIG_DIS_UNIT_NAME", length = 127)
  private String trigDisUnitName;

  @Size(max = 127)
  @Column(name = "GAP_DIS_UNIT_NAME", length = 127)
  private String gapDisUnitName;

}