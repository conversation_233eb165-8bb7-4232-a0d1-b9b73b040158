package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "INDUCTION")
public class EInduction {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)

  @Column(name = "OBJECT_ID")
  private Long objectId;

}