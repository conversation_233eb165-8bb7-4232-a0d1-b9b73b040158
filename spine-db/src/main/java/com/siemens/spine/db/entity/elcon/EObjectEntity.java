package com.siemens.spine.db.entity.elcon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "ELCON_OBJECT")
public class ElconObjectEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "elcon_object_id_seq")
    @SequenceGenerator(name = "elcon_object_id_seq", sequenceName = "elcon_object_id_seq", allocationSize = 25)
    @Column(name = "OBJECT_ID")
    @JsonProperty("object_id")
    private Long objectId;

    @Size(max = 255)
    @Column(name = "UNIQUE_ID")
    @JsonProperty("unique_id")
    private String uniqueId;

    @Size(max = 255)
    @Column(name = "OBJECT_KIND")
    @JsonProperty("object_kind")
    private String objectKind;

    @Size(max = 255)
    @Column(name = "HW_OUTFIT_NAME")
    @JsonProperty("hw_outfit_name")
    private String hwOutfitName;

    @Size(max = 255)
    @Column(name = "SW_SET_KEY")
    @JsonProperty("sw_set_key")
    private String swSetKey;

    @Size(max = 255)
    @Column(name = "ITEM_NAME")
    @JsonProperty("item_name")
    private String itemName;

    @Column(name = "SW_RELEVANT")
    @Type(type = "org.hibernate.type.NumericBooleanType")
    @JsonProperty("sw_relevant")
    private Boolean swRelevant;

    @Column(name = "PLC_SW_UNIT_ID")
    @JsonProperty("plc_sw_unit_id")
    private Long plcSwUnitId;

    @Column(name = "PLC_SW_OBJECT_NR")
    @JsonProperty("plc_sw_object_nr")
    private Long plcSwObjectNr;

    @Size(max = 255)
    @Column(name = "OBJECT_ORIGIN")
    @JsonProperty("object_origin")
    private String objectOrigin;

    @Size(max = 255)
    @Column(name = "EXTERNAL_REFERENCE")
    @JsonProperty("external_reference")
    private String externalReference;

    @Size(max = 255)
    @Column(name = "IO_BASE_ADDRESS")
    @JsonProperty("io_base_address")
    private String ioBaseAddress;

    @Size(max = 255)
    @Column(name = "LOGISTICS_PROPERTY")
    @JsonProperty("logistics_property")
    private String logisticsProperty;

    @Size(max = 255)
    @Column(name = "OBJECT_SUB_KIND")
    @JsonProperty("object_sub_kind")
    private String objectSubKind;

    @Size(max = 255)
    @Column(name = "HWC_FUNCTIONS")
    @JsonProperty("hwc_functions")
    private String hwcFunctions;

}