package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "CARRIER")
public class ECarrier {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)

  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Column(name = "INV_MOTOR_DIR")
  private Boolean invMotorDir;

  @Column(name = "CAN_NODE_ID")
  private Integer canNodeId;

  @Column(name = "NETWORK_PARENT_CELL_ID")
  private Integer networkParentCellId;

  @Column(name = "PWR_SUPPLY_UNIT_PARENT_CELL_ID")
  private Integer pwrSupplyUnitParentCellId;

  @Column(name = "PWR_COLLECTOR_PARENT_CELL_ID")
  private Integer pwrCollectorParentCellId;

}