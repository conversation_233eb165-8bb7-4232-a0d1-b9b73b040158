package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "FIELDDEVICE")
public class EFielddevice {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)

  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Size(max = 31)
  @Column(name = "FD_TYPE", length = 31)
  private String fdType;

  @Column(name = "DI_ADDR_POWER_SUPPLY")
  private Long diAddrPowerSupply;

  @Column(name = "DI_ADDR_FAN")
  private Long diAddrFan;

  @Column(name = "DI_ADDR_SITOP_SELECT")
  private Long diAddrSitopSelect;

  @Column(name = "DI_ADDR_SCALANCE")
  private Long diAddrScalance;

}