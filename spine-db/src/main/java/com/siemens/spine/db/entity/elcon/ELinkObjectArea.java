package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "LINK_OBJECT_AREA")
public class ELinkObjectArea {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @NotNull
  @Column(name = "LINK_ID", nullable = false)
  private Long linkId;

  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Column(name = "AREA_OBJECT_ID")
  private Long areaObjectId;

}