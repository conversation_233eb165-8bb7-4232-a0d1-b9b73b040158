package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "DRIVE")
public class ElconDrive {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Column(name = "ENABLE_ANA_TRC_BOXES")
  private Boolean enableAnaTrcBoxes;

  @Column(name = "MAX_SPEED_SET")
  private Integer maxSpeedSet;

  @Column(name = "DISABLE_TIME_AT_STOP")
  private Long disableTimeAtStop;

  @Column(name = "PN_OK_TIMER")
  private Long pnOkTimer;

  @Column(name = "DISABLE_BY_CONFIG")
  private Boolean disableByConfig;

}