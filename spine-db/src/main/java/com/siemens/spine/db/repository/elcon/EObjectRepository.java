package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.EObject;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elcon_object", collectionResourceRel = "elcon_object")
@ApplicationScoped
public class EObjectRepository extends AbstractRestCRUDRepository<EObject, Long> {

    @Override
    protected boolean isNew(EObject entity) {
        return entity.getObjectId() == null;
    }

}
