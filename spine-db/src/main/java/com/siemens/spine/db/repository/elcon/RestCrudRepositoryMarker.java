package com.siemens.spine.db.repository.elcon;

/**
 * Marker interface for REST repositories. Used for discovery by the REST endpoint generator.
 * Any repository implementing this interface and annotated with {@link RestRepository}
 * will be considered for automatic REST API exposure.
 * <p>
 * It's inspired by Spring Data REST
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface RestCrudRepositoryMarker {

}