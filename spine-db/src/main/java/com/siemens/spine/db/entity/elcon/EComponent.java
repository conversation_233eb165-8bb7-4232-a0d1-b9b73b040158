package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "COMPONENT")
public class EComponent {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)

  @NotNull
  @Column(name = "COMPONENT_ID", nullable = false)
  private Long componentId;

  @Size(max = 255)
  @Column(name = "COMPONENT_KIND")
  private String componentKind;

  @Column(name = "PLC_AREA_ID")
  private Long plcAreaId;

  @Column(name = "SW_BLOCK_ID")
  private Long swBlockId;

}