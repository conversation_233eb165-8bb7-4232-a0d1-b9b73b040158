package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "COMMUNICATION_UNIT")
public class ECommunicationUnit {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)

  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Size(max = 20)
  @Column(name = "COMMUNICATION_KIND", length = 20)
  private String communicationKind;

  @Size(max = 20)
  @Column(name = "CONNECTION_TYPE", length = 20)
  private String connectionType;

  @Size(max = 20)
  @Column(name = "CONNECTION_ID", length = 20)
  private String connectionId;

  @Column(name = "ACTIVE_FLAG")
  private Boolean activeFlag;

  @Size(max = 20)
  @Column(name = "REDUNDANCY_CHANNEL", length = 20)
  private String redundancyChannel;

  @Column(name = "LOCAL_CHANNEL_ID")
  private Long localChannelId;

}