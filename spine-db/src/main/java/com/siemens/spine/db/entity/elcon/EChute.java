package com.siemens.spine.db.entity.elcon;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "CHUTE")
public class EChute {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "OBJECT_ID")
  private Long objectId;

  @Column(name = "DI_ADDR_JAM")
  private Long diAddrJam;

  @Column(name = "DI_ADDR_DISABLE")
  private Long diAddrDisable;

  @Column(name = "DI_ADDR_DISABLE_REQ")
  private Long diAddrDisableReq;

  @Column(name = "DI_ADDR_READY_RECV")
  private Long diAddrReadyRecv;

  @Column(name = "DO_ADDR_PAR_RELEASED")
  private Long doAddrParReleased;

  @Column(name = "DO_ADDR_DISABLED")
  private Long doAddrDisabled;

  @Column(name = "DO_ADDR_DISABLED_CONF")
  private Long doAddrDisabledConf;

  @Size(max = 255)
  @Column(name = "PNPN_COUPLER_NAME")
  private String pnpnCouplerName;

}