package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.EDrive;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elcon_drive", collectionResourceRel = "elcon_drive")
@ApplicationScoped
public class EDriveRepository extends AbstractRestCRUDRepository<EDrive, Long> {

    @Override
    protected boolean isNew(EDrive entity) {
        return entity.getObjectId() == null;
    }

}
