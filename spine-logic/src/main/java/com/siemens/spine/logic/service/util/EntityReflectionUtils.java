package com.siemens.spine.logic.service.util;

import com.siemens.spine.logic.exception.SpineException;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Id;
import java.lang.reflect.Field;
import java.util.Objects;

/**
 * Utility service for entity reflection operations.
 * Provides methods for ID extraction, setting, and type conversion
 * commonly needed when working with JPA entities in REST operations.
 *
 * <AUTHOR> Development Team
 * @since 1.0
 */
@ApplicationScoped
@Slf4j
public class EntityReflectionUtils {

    /**
     * Converts a string ID to the appropriate type based on the target class.
     *
     * @param idStr   The string representation of the ID
     * @param idClass The target class for the ID
     * @return The converted ID object
     * @throws SpineException if conversion fails
     */
    public Object convertId(String idStr, Class<?> idClass) throws SpineException {
        Objects.requireNonNull(idStr, "ID string cannot be null");
        Objects.requireNonNull(idClass, "ID class cannot be null");

        try {
            if (idClass == Long.class || idClass == long.class) {
                return Long.parseLong(idStr);
            } else if (idClass == Integer.class || idClass == int.class) {
                return Integer.parseInt(idStr);
            } else if (idClass == String.class) {
                return idStr;
            } else if (idClass == Short.class || idClass == short.class) {
                return Short.parseShort(idStr);
            } else if (idClass == Byte.class || idClass == byte.class) {
                return Byte.parseByte(idStr);
            } else {
                // For unsupported types, return as string and let the repository handle it
                log.warn("Unsupported ID type: {}. Returning as string.", idClass.getSimpleName());
                return idStr;
            }
        } catch (NumberFormatException e) {
            throw new SpineException("Failed to convert ID '" + idStr + "' to type " + idClass.getSimpleName(), e);
        }
    }

    /**
     * Extracts the ID value from an entity using reflection.
     * Looks for fields annotated with @Id.
     *
     * @param entity The entity from which to extract the ID
     * @return The ID value, or null if not found
     * @throws SpineException if reflection operations fail
     */
    public Object extractId(Object entity) throws SpineException {
        Objects.requireNonNull(entity, "Entity cannot be null");

        Class<?> clazz = entity.getClass();
        Field idField = findIdField(clazz);
        
        if (idField == null) {
            throw new SpineException("No @Id annotated field found in entity class: " + clazz.getSimpleName());
        }

        return getFieldValue(entity, idField);
    }

    /**
     * Sets the ID value on an entity using reflection.
     * Looks for fields annotated with @Id.
     *
     * @param entity The entity on which to set the ID
     * @param id     The ID value to set
     * @throws SpineException if reflection operations fail
     */
    public void setIdOnEntity(Object entity, Object id) throws SpineException {
        Objects.requireNonNull(entity, "Entity cannot be null");
        Objects.requireNonNull(id, "ID cannot be null");

        Class<?> clazz = entity.getClass();
        Field idField = findIdField(clazz);
        
        if (idField == null) {
            throw new SpineException("No @Id annotated field found in entity class: " + clazz.getSimpleName());
        }

        setFieldValue(entity, idField, id);
    }

    /**
     * Finds the field annotated with @Id in the given class.
     * Searches through the class hierarchy if necessary.
     *
     * @param clazz The class to search
     * @return The @Id annotated field, or null if not found
     */
    private Field findIdField(Class<?> clazz) {
        // Search in current class
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(Id.class)) {
                return field;
            }
        }

        // Search in superclass if not found
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null && superClass != Object.class) {
            return findIdField(superClass);
        }

        return null;
    }

    /**
     * Gets the value of a field from an entity using reflection.
     *
     * @param entity The entity object
     * @param field  The field to read
     * @return The field value
     * @throws SpineException if reflection operations fail
     */
    private Object getFieldValue(Object entity, Field field) throws SpineException {
        try {
            field.setAccessible(true);
            return field.get(entity);
        } catch (IllegalAccessException e) {
            throw new SpineException("Failed to access field '" + field.getName() + "' in entity class: " + 
                                   entity.getClass().getSimpleName(), e);
        } catch (SecurityException e) {
            throw new SpineException("Security exception accessing field '" + field.getName() + "' in entity class: " + 
                                   entity.getClass().getSimpleName(), e);
        }
    }

    /**
     * Sets the value of a field on an entity using reflection.
     *
     * @param entity The entity object
     * @param field  The field to set
     * @param value  The value to set
     * @throws SpineException if reflection operations fail
     */
    private void setFieldValue(Object entity, Field field, Object value) throws SpineException {
        try {
            field.setAccessible(true);
            field.set(entity, value);
        } catch (IllegalAccessException e) {
            throw new SpineException("Failed to set field '" + field.getName() + "' in entity class: " + 
                                   entity.getClass().getSimpleName(), e);
        } catch (SecurityException e) {
            throw new SpineException("Security exception setting field '" + field.getName() + "' in entity class: " + 
                                   entity.getClass().getSimpleName(), e);
        } catch (IllegalArgumentException e) {
            throw new SpineException("Invalid argument setting field '" + field.getName() + "' with value type: " + 
                                   (value != null ? value.getClass().getSimpleName() : "null"), e);
        }
    }
}
