package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.elcon.RestCrudRepositoryMarker;
import com.siemens.spine.db.repository.elcon.RestRepository;
import com.siemens.spine.logic.service.util.ReflectionUtils;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Any;
import javax.enterprise.inject.Instance;
import javax.inject.Inject;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Service responsible for discovering and managing REST endpoints based on repository annotations.
 * This class scans for repositories marked with {@link RestRepository} annotation and generates
 * endpoint information for automatic REST API exposure.
 *
 * <p>Thread-safe implementation that caches discovered endpoints for performance.</p>
 *
 * <AUTHOR> Development Team
 * @since 1.0
 */
@ApplicationScoped
public class RestEndpointGenerator {

    private static final Logger LOGGER = Logger.getLogger(RestEndpointGenerator.class.getName());
    private static final String TARGET_PACKAGE = "com.siemens.spine.db.repository.elcon";

    private final Instance<RestCrudRepositoryMarker> repositoryBeans;
    private final ReflectionUtils reflectionUtils;

    // Thread-safe cache for generated endpoints
    private final ConcurrentMap<String, RestEndpointInfo> endpointCache = new ConcurrentHashMap<>();
    private volatile boolean initialized = false;

    @Inject
    public RestEndpointGenerator(@Any Instance<RestCrudRepositoryMarker> repositoryBeans,
                                ReflectionUtils reflectionUtils) {
        this.repositoryBeans = Objects.requireNonNull(repositoryBeans, "Repository beans cannot be null");
        this.reflectionUtils = Objects.requireNonNull(reflectionUtils, "ReflectionUtils cannot be null");
    }

    @PostConstruct
    public void initialize() {
        try {
            discoverAndCacheEndpoints();
            initialized = true;
            LOGGER.info("Successfully initialized RestEndpointGenerator with " + endpointCache.size() + " endpoints");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Failed to initialize RestEndpointGenerator", e);
            throw new IllegalStateException("RestEndpointGenerator initialization failed", e);
        }
    }

    /**
     * Discovers and caches all REST endpoints from annotated repositories.
     * This method is called during initialization and should not be called directly.
     */
    private void discoverAndCacheEndpoints() {
        LOGGER.fine("Starting endpoint discovery process");

        List<RestEndpointInfo> discoveredEndpoints = StreamSupport.stream(repositoryBeans.spliterator(), false)
                .map(this::processRepositoryBean)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // Cache all discovered endpoints
        discoveredEndpoints.forEach(endpoint ->
            endpointCache.put(endpoint.path(), endpoint));

        LOGGER.info("Discovered " + discoveredEndpoints.size() + " REST endpoints");
    }

    /**
     * Processes a single repository bean to extract endpoint information.
     *
     * @param repositoryBean the repository bean to process
     * @return RestEndpointInfo if valid endpoint found, null otherwise
     */
    private RestEndpointInfo processRepositoryBean(Object repositoryBean) {
        try {
            Class<?> actualClass = reflectionUtils.resolveActualClass(repositoryBean);

            if (!reflectionUtils.isInPackage(actualClass, TARGET_PACKAGE)) {
                return null;
            }

            return reflectionUtils.getAnnotation(actualClass, RestRepository.class)
                    .filter(RestRepository::exported)
                    .map(annotation -> createEndpointInfo(repositoryBean, annotation, actualClass))
                    .orElse(null);

        } catch (Exception e) {
            LOGGER.log(Level.WARNING, "Failed to process repository bean: " + repositoryBean.getClass().getName(), e);
            return null;
        }
    }

    /**
     * Creates endpoint information from repository bean and annotation.
     *
     * @param repository the repository instance
     * @param annotation the RestRepository annotation
     * @param repositoryClass the repository class
     * @return RestEndpointInfo instance
     */
    private RestEndpointInfo createEndpointInfo(Object repository, RestRepository annotation, Class<?> repositoryClass) {
        Optional<Class<?>> entityClass = reflectionUtils.extractEntityType(repositoryClass);
        Optional<Class<?>> idClass = reflectionUtils.extractIdType(repositoryClass);

        if (entityClass.isEmpty()) {
            LOGGER.warning("Could not determine entity type for repository: " + repositoryClass.getName());
            return null;
        }

        String path = determineEndpointPath(annotation, entityClass.get());

        return new RestEndpointInfo(
            repository,
            entityClass.get(),
            idClass.orElse(null),
            path
        );
    }

    /**
     * Determines the REST endpoint path based on annotation and entity class.
     *
     * @param annotation the RestRepository annotation
     * @param entityClass the entity class
     * @return the endpoint path
     */
    private String determineEndpointPath(RestRepository annotation, Class<?> entityClass) {
        if (!annotation.path().isEmpty()) {
            return ensureLeadingSlash(annotation.path());
        }

        // Generate default path from entity class name
        String defaultPath = entityClass.getSimpleName().toLowerCase() + "s";
        return ensureLeadingSlash(defaultPath);
    }

    /**
     * Ensures the path starts with a forward slash.
     *
     * @param path the path to check
     * @return path with leading slash
     */
    private String ensureLeadingSlash(String path) {
        return path.startsWith("/") ? path : "/" + path;
    }

    /**
     * Returns all discovered REST endpoints.
     * This method is thread-safe and returns a defensive copy of the endpoints.
     *
     * @return immutable list of REST endpoint information
     * @throws IllegalStateException if the generator is not properly initialized
     */
    public List<RestEndpointInfo> getGeneratedEndpoints() {
        ensureInitialized();
        return Collections.unmodifiableList(
            endpointCache.values().stream().collect(Collectors.toList())
        );
    }

    /**
     * Finds a specific endpoint by its path.
     *
     * @param path the endpoint path to search for
     * @return optional containing the endpoint info, empty if not found
     * @throws IllegalArgumentException if path is null or empty
     */
    public Optional<RestEndpointInfo> findEndpointByPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            throw new IllegalArgumentException("Path cannot be null or empty");
        }

        ensureInitialized();
        String normalizedPath = ensureLeadingSlash(path.trim());
        return Optional.ofNullable(endpointCache.get(normalizedPath));
    }

    /**
     * Returns the number of discovered endpoints.
     *
     * @return the count of endpoints
     */
    public int getEndpointCount() {
        ensureInitialized();
        return endpointCache.size();
    }

    /**
     * Checks if the generator has been properly initialized.
     *
     * @throws IllegalStateException if not initialized
     */
    private void ensureInitialized() {
        if (!initialized) {
            throw new IllegalStateException("RestEndpointGenerator is not properly initialized");
        }
    }

    /**
     * Forces a refresh of the endpoint cache.
     * This method should be used sparingly as it's expensive.
     *
     * @return the number of endpoints discovered after refresh
     */
    public int refreshEndpoints() {
        LOGGER.info("Refreshing REST endpoints cache");
        endpointCache.clear();
        discoverAndCacheEndpoints();
        return endpointCache.size();
    }

    /**
     * Immutable record representing REST endpoint information.
     * Contains all necessary information to expose a repository as a REST endpoint.
     *
     * @param repository The repository instance that handles the data operations
     * @param entityClass The entity class that this endpoint manages
     * @param idClass The ID class for the entity (may be null if not determinable)
     * @param path The REST endpoint path (always starts with '/')
     *
     * @throws IllegalArgumentException if repository, entityClass, or path is null
     * @throws IllegalArgumentException if path is empty or doesn't start with '/'
     */
    public record RestEndpointInfo(
            Object repository,
            Class<?> entityClass,
            Class<?> idClass,
            String path) {

        public RestEndpointInfo {
            Objects.requireNonNull(repository, "Repository cannot be null");
            Objects.requireNonNull(entityClass, "Entity class cannot be null");
            Objects.requireNonNull(path, "Path cannot be null");

            if (path.trim().isEmpty()) {
                throw new IllegalArgumentException("Path cannot be empty");
            }

            if (!path.startsWith("/")) {
                throw new IllegalArgumentException("Path must start with '/'");
            }
        }

        /**
         * Returns the simple name of the entity class.
         *
         * @return the entity class simple name
         */
        public String getEntityName() {
            return entityClass.getSimpleName();
        }

        /**
         * Returns the simple name of the ID class, or "Unknown" if ID class is null.
         *
         * @return the ID class simple name or "Unknown"
         */
        public String getIdTypeName() {
            return idClass != null ? idClass.getSimpleName() : "Unknown";
        }

        /**
         * Checks if this endpoint has a determinable ID type.
         *
         * @return true if ID class is not null
         */
        public boolean hasIdType() {
            return idClass != null;
        }
    }
}

}