package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.CrudRepository;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.util.EntityReflectionUtils;
import com.siemens.spine.logic.service.util.JsonSerializationService;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Service layer for handling REST entity operations.
 * This service encapsulates the business logic for CRUD operations on entities
 * exposed through REST endpoints, providing a clean separation between the
 * resource layer and repository layer.
 *
 * <AUTHOR> Development Team
 * @since 1.0
 */
@ApplicationScoped
public class RestEntityService {

    private final RestEndpointGenerator endpointGenerator;
    private final EntityReflectionUtils entityReflectionUtils;
    private final JsonSerializationService jsonSerializationService;

    /**
     * Constructor for dependency injection.
     *
     * @param endpointGenerator      Service for managing REST endpoints
     * @param entityReflectionUtils  Utility for entity reflection operations
     * @param jsonSerializationService Service for JSON serialization/deserialization
     */
    @Inject
    public RestEntityService(RestEndpointGenerator endpointGenerator,
                           EntityReflectionUtils entityReflectionUtils,
                           JsonSerializationService jsonSerializationService) {
        this.endpointGenerator = Objects.requireNonNull(endpointGenerator, "EndpointGenerator cannot be null");
        this.entityReflectionUtils = Objects.requireNonNull(entityReflectionUtils, "EntityReflectionUtils cannot be null");
        this.jsonSerializationService = Objects.requireNonNull(jsonSerializationService, "JsonSerializationService cannot be null");
    }

    /**
     * Retrieves all entities for the specified entity path.
     *
     * @param entityPath The entity path identifier
     * @return List of all entities
     * @throws SpineException if the entity path is not found or operation fails
     */
    public List<Object> getAllEntities(String entityPath) throws SpineException {
        RestEndpointGenerator.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.findAll();
    }

    /**
     * Retrieves a single entity by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @return Optional containing the entity if found
     * @throws SpineException if the entity path is not found or ID conversion fails
     */
    public Optional<Object> getEntityById(String entityPath, String idStr) throws SpineException {
        RestEndpointGenerator.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = entityReflectionUtils.convertId(idStr, endpoint.idClass());
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.findById(id);
    }

    /**
     * Creates a new entity from JSON data.
     *
     * @param entityPath The entity path identifier
     * @param jsonBody   The JSON representation of the entity
     * @return The created entity with generated ID
     * @throws SpineException if creation fails or JSON is invalid
     */
    public Object createEntity(String entityPath, String jsonBody) throws SpineException {
        RestEndpointGenerator.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object entity = jsonSerializationService.deserializeEntity(jsonBody, endpoint.entityClass());
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.save(entity);
    }

    /**
     * Updates an existing entity with new data from JSON.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @param jsonBody   The JSON representation of the updated entity
     * @return The updated entity
     * @throws SpineException if update fails, entity not found, or JSON is invalid
     */
    public Object updateEntity(String entityPath, String idStr, String jsonBody) throws SpineException {
        RestEndpointGenerator.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = entityReflectionUtils.convertId(idStr, endpoint.idClass());
        CrudRepository<Object, Object> repository = getRepository(endpoint);

        // Check if entity exists
        if (!repository.existsById(id)) {
            throw new SpineException("Entity not found with ID: " + idStr);
        }

        // Deserialize and set ID
        Object entity = jsonSerializationService.deserializeEntity(jsonBody, endpoint.entityClass());
        entityReflectionUtils.setIdOnEntity(entity, id);

        return repository.save(entity);
    }

    /**
     * Deletes an entity by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @throws SpineException if deletion fails or entity not found
     */
    public void deleteEntity(String entityPath, String idStr) throws SpineException {
        RestEndpointGenerator.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = entityReflectionUtils.convertId(idStr, endpoint.idClass());
        CrudRepository<Object, Object> repository = getRepository(endpoint);

        // Check if entity exists
        if (!repository.existsById(id)) {
            throw new SpineException("Entity not found with ID: " + idStr);
        }

        repository.deleteById(id);
    }

    /**
     * Checks if an entity exists by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @return true if the entity exists, false otherwise
     * @throws SpineException if the entity path is not found or ID conversion fails
     */
    public boolean entityExists(String entityPath, String idStr) throws SpineException {
        RestEndpointGenerator.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = entityReflectionUtils.convertId(idStr, endpoint.idClass());
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.existsById(id);
    }

    /**
     * Extracts the ID from a saved entity for location header generation.
     *
     * @param entity The entity from which to extract the ID
     * @return The extracted ID
     * @throws SpineException if ID extraction fails
     */
    public Object extractEntityId(Object entity) throws SpineException {
        return entityReflectionUtils.extractId(entity);
    }

    /**
     * Retrieves endpoint information for the given entity path.
     *
     * @param entityPath The entity path identifier
     * @return The endpoint information
     * @throws SpineException if the endpoint is not found
     */
    private RestEndpointGenerator.RestEndpointInfo getEndpointInfo(String entityPath) throws SpineException {
        return endpointGenerator.getGeneratedEndpoints().stream()
                .filter(info -> info.path().equals("/" + entityPath))
                .findFirst()
                .orElseThrow(() -> new SpineException("Entity path not found: " + entityPath));
    }

    /**
     * Safely casts the repository from the endpoint info.
     *
     * @param endpoint The endpoint information
     * @return The typed repository
     */
    @SuppressWarnings("unchecked")
    private CrudRepository<Object, Object> getRepository(RestEndpointGenerator.RestEndpointInfo endpoint) {
        return (CrudRepository<Object, Object>) endpoint.repository();
    }
}
