package com.siemens.spine.logic.service.util;

import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * Utility class for common reflection operations.
 * Provides safe and robust methods for working with generic types, proxies, and annotations.
 *
 * <AUTHOR> Development Team
 * @since 1.0
 */
@ApplicationScoped
@Slf4j
public class ReflectionUtils {

    private static final String PROXY_MARKER = "$Proxy$";

    /**
     * Resolves the actual class from a potentially proxied object.
     * Handles CDI proxies and other proxy implementations.
     *
     * @param proxyObject the potentially proxied object
     * @return the actual class, never null
     * @throws IllegalArgumentException if proxyObject is null
     */
    public Class<?> resolveActualClass(Object proxyObject) {
        Objects.requireNonNull(proxyObject, "Proxy object cannot be null");

        Class<?> proxyClass = proxyObject.getClass();

        if (isProxy(proxyClass)) {
            return resolveProxyTarget(proxyClass);
        }

        return proxyClass;
    }

    /**
     * Checks if a class is a proxy class.
     *
     * @param clazz the class to check
     * @return true if the class is a proxy
     */
    public boolean isProxy(Class<?> clazz) {
        return clazz != null && clazz.getName().contains(PROXY_MARKER);
    }

    /**
     * Resolves the target class from a proxy class.
     *
     * @param proxyClass the proxy class
     * @return the target class
     */
    private Class<?> resolveProxyTarget(Class<?> proxyClass) {
        Class<?> superclass = proxyClass.getSuperclass();
        if (superclass != null && superclass != Object.class) {
            return superclass;
        }

        Class<?>[] interfaces = proxyClass.getInterfaces();
        if (interfaces.length > 0) {
            return interfaces[0];
        }

        log.warn("Could not resolve proxy target for class: {}", proxyClass.getName());
        return proxyClass;
    }

    /**
     * Extracts generic type arguments from a class's generic superclass.
     *
     * @param clazz the class to analyze
     * @return array of generic type classes, empty array if none found
     * @throws IllegalArgumentException if clazz is null
     */
    public Class<?>[] extractGenericTypes(Class<?> clazz) {
        Objects.requireNonNull(clazz, "Class cannot be null");

        try {
            Type genericSuperclass = clazz.getGenericSuperclass();

            if (genericSuperclass instanceof ParameterizedType parameterizedType) {
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

                return Arrays.stream(actualTypeArguments)
                        .filter(Class.class::isInstance)
                        .map(Class.class::cast)
                        .toArray(Class<?>[]::new);
            }

            return new Class<?>[0];
        } catch (Exception e) {
            log.error("Failed to extract generic types from class: {}", clazz.getName(), e);
            return new Class<?>[0];
        }
    }

    /**
     * Safely extracts the entity type (first generic parameter) from a repository class.
     *
     * @param repositoryClass the repository class
     * @return optional containing the entity class, empty if not found
     */
    public Optional<Class<?>> extractEntityType(Class<?> repositoryClass) {
        Class<?>[] genericTypes = extractGenericTypes(repositoryClass);
        return genericTypes.length > 0 ? Optional.of(genericTypes[0]) : Optional.empty();
    }

    /**
     * Safely extracts the ID type (second generic parameter) from a repository class.
     *
     * @param repositoryClass the repository class
     * @return optional containing the ID class, empty if not found
     */
    public Optional<Class<?>> extractIdType(Class<?> repositoryClass) {
        Class<?>[] genericTypes = extractGenericTypes(repositoryClass);
        return genericTypes.length > 1 ? Optional.of(genericTypes[1]) : Optional.empty();
    }

    /**
     * Checks if a class belongs to a specific package.
     *
     * @param clazz       the class to check
     * @param packageName the target package name
     * @return true if the class belongs to the package
     */
    public boolean isInPackage(Class<?> clazz, String packageName) {
        if (clazz == null || packageName == null) {
            return false;
        }

        Package classPackage = clazz.getPackage();
        return classPackage != null && packageName.equals(classPackage.getName());
    }

    /**
     * Safely retrieves an annotation from a class.
     *
     * @param clazz           the class to check
     * @param annotationClass the annotation class
     * @param <T>             the annotation type
     * @return optional containing the annotation, empty if not found
     */
    public <T extends java.lang.annotation.Annotation> Optional<T> getAnnotation(
            Class<?> clazz, Class<T> annotationClass) {

        if (clazz == null || annotationClass == null) {
            return Optional.empty();
        }

        try {
            return Optional.ofNullable(clazz.getAnnotation(annotationClass));
        } catch (Exception e) {
            log.error("Failed to retrieve annotation {} from class {}", annotationClass.getSimpleName(),
                    clazz.getName(), e);
            return Optional.empty();
        }
    }

}
