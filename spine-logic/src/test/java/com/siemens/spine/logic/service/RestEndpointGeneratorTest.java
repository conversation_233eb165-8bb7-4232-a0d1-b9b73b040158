package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.elcon.RestCrudRepositoryMarker;
import com.siemens.spine.db.repository.elcon.RestRepository;
import com.siemens.spine.logic.service.util.ReflectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.enterprise.inject.Instance;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RestEndpointGeneratorTest {

    @Mock
    private Instance<RestCrudRepositoryMarker> repositoryBeans;

    @Mock
    private ReflectionUtils reflectionUtils;

    @Mock
    private TestRepository testRepository;

    private RestEndpointGenerator endpointGenerator;

    // Test entity and repository classes
    public static class TestEntity {
        private Long id;
        private String name;
    }

    @RestRepository(path = "test-entities", exported = true)
    public static class TestRepository implements RestCrudRepositoryMarker {
    }

    @RestRepository(exported = false)
    public static class NonExportedRepository implements RestCrudRepositoryMarker {
    }

    @BeforeEach
    void setUp() {
        endpointGenerator = new RestEndpointGenerator(repositoryBeans, reflectionUtils);
    }

    @Test
    void testInitialization_Success() {
        // Given
        when(repositoryBeans.spliterator()).thenReturn(Arrays.asList(testRepository).spliterator());
        when(reflectionUtils.resolveActualClass(testRepository)).thenReturn(TestRepository.class);
        when(reflectionUtils.isInPackage(TestRepository.class, "com.siemens.spine.db.repository.elcon")).thenReturn(true);
        when(reflectionUtils.getAnnotation(TestRepository.class, RestRepository.class))
                .thenReturn(Optional.of(TestRepository.class.getAnnotation(RestRepository.class)));
        when(reflectionUtils.extractEntityType(TestRepository.class)).thenReturn(Optional.of(TestEntity.class));
        when(reflectionUtils.extractIdType(TestRepository.class)).thenReturn(Optional.of(Long.class));

        // When
        endpointGenerator.initialize();

        // Then
        assertEquals(1, endpointGenerator.getEndpointCount());
        List<RestEndpointGenerator.RestEndpointInfo> endpoints = endpointGenerator.getGeneratedEndpoints();
        assertEquals(1, endpoints.size());
        
        RestEndpointGenerator.RestEndpointInfo endpoint = endpoints.get(0);
        assertEquals(testRepository, endpoint.repository());
        assertEquals(TestEntity.class, endpoint.entityClass());
        assertEquals(Long.class, endpoint.idClass());
        assertEquals("/test-entities", endpoint.path());
    }

    @Test
    void testInitialization_WithNonExportedRepository() {
        // Given
        TestRepository nonExportedRepo = mock(TestRepository.class);
        when(repositoryBeans.spliterator()).thenReturn(Arrays.asList(nonExportedRepo).spliterator());
        when(reflectionUtils.resolveActualClass(nonExportedRepo)).thenReturn(NonExportedRepository.class);
        when(reflectionUtils.isInPackage(NonExportedRepository.class, "com.siemens.spine.db.repository.elcon")).thenReturn(true);
        when(reflectionUtils.getAnnotation(NonExportedRepository.class, RestRepository.class))
                .thenReturn(Optional.of(NonExportedRepository.class.getAnnotation(RestRepository.class)));

        // When
        endpointGenerator.initialize();

        // Then
        assertEquals(0, endpointGenerator.getEndpointCount());
        assertTrue(endpointGenerator.getGeneratedEndpoints().isEmpty());
    }

    @Test
    void testFindEndpointByPath_Success() {
        // Given
        setupSuccessfulInitialization();

        // When
        Optional<RestEndpointGenerator.RestEndpointInfo> result = 
                endpointGenerator.findEndpointByPath("/test-entities");

        // Then
        assertTrue(result.isPresent());
        assertEquals("/test-entities", result.get().path());
        assertEquals(TestEntity.class, result.get().entityClass());
    }

    @Test
    void testFindEndpointByPath_NotFound() {
        // Given
        setupSuccessfulInitialization();

        // When
        Optional<RestEndpointGenerator.RestEndpointInfo> result = 
                endpointGenerator.findEndpointByPath("/non-existent");

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testFindEndpointByPath_NullPath() {
        // Given
        setupSuccessfulInitialization();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
                endpointGenerator.findEndpointByPath(null));
    }

    @Test
    void testFindEndpointByPath_EmptyPath() {
        // Given
        setupSuccessfulInitialization();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
                endpointGenerator.findEndpointByPath(""));
    }

    @Test
    void testRefreshEndpoints() {
        // Given
        setupSuccessfulInitialization();
        assertEquals(1, endpointGenerator.getEndpointCount());

        // When
        int count = endpointGenerator.refreshEndpoints();

        // Then
        assertEquals(1, count);
        assertEquals(1, endpointGenerator.getEndpointCount());
    }

    @Test
    void testGetGeneratedEndpoints_BeforeInitialization() {
        // When & Then
        assertThrows(IllegalStateException.class, () -> 
                endpointGenerator.getGeneratedEndpoints());
    }

    @Test
    void testRestEndpointInfo_Validation() {
        // Test valid creation
        RestEndpointGenerator.RestEndpointInfo validInfo = 
                new RestEndpointGenerator.RestEndpointInfo(testRepository, TestEntity.class, Long.class, "/test");
        
        assertEquals(testRepository, validInfo.repository());
        assertEquals(TestEntity.class, validInfo.entityClass());
        assertEquals(Long.class, validInfo.idClass());
        assertEquals("/test", validInfo.path());
        assertEquals("TestEntity", validInfo.getEntityName());
        assertEquals("Long", validInfo.getIdTypeName());
        assertTrue(validInfo.hasIdType());

        // Test null repository
        assertThrows(NullPointerException.class, () -> 
                new RestEndpointGenerator.RestEndpointInfo(null, TestEntity.class, Long.class, "/test"));

        // Test null entity class
        assertThrows(NullPointerException.class, () -> 
                new RestEndpointGenerator.RestEndpointInfo(testRepository, null, Long.class, "/test"));

        // Test null path
        assertThrows(NullPointerException.class, () -> 
                new RestEndpointGenerator.RestEndpointInfo(testRepository, TestEntity.class, Long.class, null));

        // Test empty path
        assertThrows(IllegalArgumentException.class, () -> 
                new RestEndpointGenerator.RestEndpointInfo(testRepository, TestEntity.class, Long.class, ""));

        // Test path without leading slash
        assertThrows(IllegalArgumentException.class, () -> 
                new RestEndpointGenerator.RestEndpointInfo(testRepository, TestEntity.class, Long.class, "test"));
    }

    @Test
    void testRestEndpointInfo_WithNullIdClass() {
        // Given
        RestEndpointGenerator.RestEndpointInfo info = 
                new RestEndpointGenerator.RestEndpointInfo(testRepository, TestEntity.class, null, "/test");

        // Then
        assertNull(info.idClass());
        assertEquals("Unknown", info.getIdTypeName());
        assertFalse(info.hasIdType());
    }

    private void setupSuccessfulInitialization() {
        when(repositoryBeans.spliterator()).thenReturn(Arrays.asList(testRepository).spliterator());
        when(reflectionUtils.resolveActualClass(testRepository)).thenReturn(TestRepository.class);
        when(reflectionUtils.isInPackage(TestRepository.class, "com.siemens.spine.db.repository.elcon")).thenReturn(true);
        when(reflectionUtils.getAnnotation(TestRepository.class, RestRepository.class))
                .thenReturn(Optional.of(TestRepository.class.getAnnotation(RestRepository.class)));
        when(reflectionUtils.extractEntityType(TestRepository.class)).thenReturn(Optional.of(TestEntity.class));
        when(reflectionUtils.extractIdType(TestRepository.class)).thenReturn(Optional.of(Long.class));
        
        endpointGenerator.initialize();
    }
}
