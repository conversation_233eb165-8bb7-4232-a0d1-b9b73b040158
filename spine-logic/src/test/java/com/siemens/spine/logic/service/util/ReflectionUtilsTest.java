package com.siemens.spine.logic.service.util;

import com.siemens.spine.db.repository.elcon.RestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class ReflectionUtilsTest {

    private ReflectionUtils reflectionUtils;

    // Test classes for generic type extraction
    public static class TestEntity {
        private Long id;
        private String name;
    }

    public static class BaseRepository<T, ID> {
    }

    public static class TestRepository extends BaseRepository<TestEntity, Long> {
    }

    @RestRepository(path = "test", exported = true)
    public static class AnnotatedRepository {
    }

    public static class NonAnnotatedRepository {
    }

    @BeforeEach
    void setUp() {
        reflectionUtils = new ReflectionUtils();
    }

    @Test
    void testResolveActualClass_RegularClass() {
        // Given
        TestEntity entity = new TestEntity();

        // When
        Class<?> result = reflectionUtils.resolveActualClass(entity);

        // Then
        assertEquals(TestEntity.class, result);
    }

    @Test
    void testResolveActualClass_NullObject() {
        // When & Then
        assertThrows(NullPointerException.class, () -> 
                reflectionUtils.resolveActualClass(null));
    }

    @Test
    void testIsProxy_RegularClass() {
        // When
        boolean result = reflectionUtils.isProxy(TestEntity.class);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsProxy_NullClass() {
        // When
        boolean result = reflectionUtils.isProxy(null);

        // Then
        assertFalse(result);
    }

    @Test
    void testExtractGenericTypes_WithGenerics() {
        // When
        Class<?>[] result = reflectionUtils.extractGenericTypes(TestRepository.class);

        // Then
        assertEquals(2, result.length);
        assertEquals(TestEntity.class, result[0]);
        assertEquals(Long.class, result[1]);
    }

    @Test
    void testExtractGenericTypes_WithoutGenerics() {
        // When
        Class<?>[] result = reflectionUtils.extractGenericTypes(TestEntity.class);

        // Then
        assertEquals(0, result.length);
    }

    @Test
    void testExtractGenericTypes_NullClass() {
        // When & Then
        assertThrows(NullPointerException.class, () -> 
                reflectionUtils.extractGenericTypes(null));
    }

    @Test
    void testExtractEntityType_Success() {
        // When
        Optional<Class<?>> result = reflectionUtils.extractEntityType(TestRepository.class);

        // Then
        assertTrue(result.isPresent());
        assertEquals(TestEntity.class, result.get());
    }

    @Test
    void testExtractEntityType_NoGenerics() {
        // When
        Optional<Class<?>> result = reflectionUtils.extractEntityType(TestEntity.class);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testExtractIdType_Success() {
        // When
        Optional<Class<?>> result = reflectionUtils.extractIdType(TestRepository.class);

        // Then
        assertTrue(result.isPresent());
        assertEquals(Long.class, result.get());
    }

    @Test
    void testExtractIdType_NoGenerics() {
        // When
        Optional<Class<?>> result = reflectionUtils.extractIdType(TestEntity.class);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testIsInPackage_Success() {
        // When
        boolean result = reflectionUtils.isInPackage(
                TestEntity.class, 
                "com.siemens.spine.logic.service.util");

        // Then
        assertTrue(result);
    }

    @Test
    void testIsInPackage_DifferentPackage() {
        // When
        boolean result = reflectionUtils.isInPackage(
                TestEntity.class, 
                "com.different.package");

        // Then
        assertFalse(result);
    }

    @Test
    void testIsInPackage_NullClass() {
        // When
        boolean result = reflectionUtils.isInPackage(null, "com.test.package");

        // Then
        assertFalse(result);
    }

    @Test
    void testIsInPackage_NullPackage() {
        // When
        boolean result = reflectionUtils.isInPackage(TestEntity.class, null);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetAnnotation_Success() {
        // When
        Optional<RestRepository> result = reflectionUtils.getAnnotation(
                AnnotatedRepository.class, RestRepository.class);

        // Then
        assertTrue(result.isPresent());
        assertEquals("test", result.get().path());
        assertTrue(result.get().exported());
    }

    @Test
    void testGetAnnotation_NotPresent() {
        // When
        Optional<RestRepository> result = reflectionUtils.getAnnotation(
                NonAnnotatedRepository.class, RestRepository.class);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testGetAnnotation_NullClass() {
        // When
        Optional<RestRepository> result = reflectionUtils.getAnnotation(
                null, RestRepository.class);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testGetAnnotation_NullAnnotationClass() {
        // When
        Optional<RestRepository> result = reflectionUtils.getAnnotation(
                AnnotatedRepository.class, null);

        // Then
        assertFalse(result.isPresent());
    }
}
