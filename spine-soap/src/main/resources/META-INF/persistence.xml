<!--needed to generate ddl from entities-->
<persistence xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns="http://xmlns.jcp.org/xml/ns/persistence"
             xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence http://xmlns.jcp.org/xml/ns/persistence/persistence_2_1.xsd"
             version="2.1">

    <persistence-unit name="spine-core" transaction-type="JTA">
        <provider>org.hibernate.jpa.HibernatePersistenceProvider</provider>
        <jta-data-source>spineCoreDS</jta-data-source>
        <class>com.siemens.spine.db.entity.GroupEntity</class>
        <class>com.siemens.spine.db.entity.ConnectionPointEntity</class>
        <class>com.siemens.spine.db.entity.NeighborConnectionEntity</class>
        <class>com.siemens.spine.db.entity.ComponentEntity</class>
        <class>com.siemens.spine.db.entity.ChangeGroupEntity</class>
        <class>com.siemens.spine.db.entity.ComponentStatusEntity</class>
        <class>com.siemens.spine.db.entity.ProjectEntity</class>
        <class>com.siemens.spine.db.entity.DecompositionEntity</class>
        <class>com.siemens.spine.db.entity.CounterEntity</class>
        <class>com.siemens.spine.db.entity.PositionEntity</class>
        <class>com.siemens.spine.db.entity.TypeEntity</class>
        <class>com.siemens.spine.db.entity.StateEntity</class>
        <class>com.siemens.spine.db.entity.GroupToProjectAndRoleEntity</class>
        <class>com.siemens.spine.db.entity.DecompositionAttributesEntity</class>
        <class>com.siemens.spine.db.entity.StateEntity</class>
        <class>com.siemens.spine.db.entity.OperationToRoleEntity</class>
        <class>com.siemens.spine.db.entity.SpineUserEntity</class>
        <class>com.siemens.spine.db.entity.ChangeGroupEntity</class>
        <class>com.siemens.spine.db.entity.UserToGroupEntity</class>
        <class>com.siemens.spine.db.entity.DriveAssignmentDataEntity</class>
        <class>com.siemens.spine.db.entity.ProjectViewGroupMappingEntity</class>
        <class>com.siemens.spine.db.entity.RevisionInfoEntity</class>
        <class>com.siemens.spine.db.entity.ProjectComponentVersionEntity</class>
        <class>com.siemens.spine.db.entity.QueryTemplateEntity</class>
        <class>com.siemens.spine.db.entity.elcon.EObject</class>
        <class>com.siemens.spine.db.entity.elcon.EDrive</class>
        <validation-mode>NONE</validation-mode>
        <!-- Hibernate properties are loaded from hibernate.properties in spine-db module -->
        <properties>
            <!-- Environment-specific properties can be overridden here -->
            <property name="hibernate.dialect" value="org.hibernate.dialect.PostgreSQL10Dialect"/>

            <property name="hibernate.jdbc.batch_size" value="50"/>
            <property name="hibernate.order_inserts" value="true"/>
            <property name="hibernate.order_updates" value="true"/>
            <property name="hibernate.jdbc.batch_versioned_data" value="true"/>
            <property name="hibernate.batch_fetch_style" value="PADDED"/>

            <!-- Statistics and logging configuration -->
            <property name="hibernate.generate_statistics" value="true"/>
            <property name="hibernate.show_sql" value="true"/> <!-- Show SQL in console -->
            <property name="hibernate.format_sql" value="true"/> <!-- Show SQL formatted -->

            <!-- Envers Configuration -->
            <property name="org.hibernate.envers.audit_table_suffix" value="_AUD"/>
            <property name="org.hibernate.envers.revision_field_name" value="REV"/>
            <property name="org.hibernate.envers.revision_type_field_name" value="REVTYPE"/>
            <property name="org.hibernate.envers.store_data_at_delete" value="true"/>
            <property name="org.hibernate.envers.global_with_modified_flag" value="true"/>
        </properties>

    </persistence-unit>

</persistence>
